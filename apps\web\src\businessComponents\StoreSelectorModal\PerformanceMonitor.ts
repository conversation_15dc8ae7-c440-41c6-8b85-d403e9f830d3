/**
 * 门店选择器性能监控工具
 * 用于监控关键流程的耗时，帮助开发者快速定位性能瓶颈
 */

// ==================== 类型定义 ====================

/**
 * 元数据类型
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type MetadataType = Record<string, any>

/**
 * 性能监控数据接口
 */
interface PerformanceData {
  startTime: number
  startTimestamp: number
  endTime?: number
  duration?: number
  metadata?: MetadataType
  parentOperation?: string
  level?: number
}

/**
 * 性能监控结果接口
 */
interface PerformanceResult {
  operation: string
  duration: number
  startTime: number
  startTimestamp: number
  endTime: number
  endTimestamp: number
  metadata?: MetadataType
  parentOperation?: string
  level?: number
}

// ==================== 性能监控类 ====================

class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private timers: Map<string, PerformanceData> = new Map()
  private results: PerformanceResult[] = []
  private isEnabled: boolean = process.env.NODE_ENV !== 'production'
  private operationStack: string[] = []

  /**
   * 获取单例实例
   */
  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  /**
   * 启用/禁用性能监控
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  /**
   * 开始计时
   */
  start(operation: string, metadata?: MetadataType): void {
    if (!this.isEnabled) return

    const startTime = performance.now()
    const startTimestamp = Date.now()
    const parentOperation = this.operationStack[this.operationStack.length - 1]
    const level = this.operationStack.length

    this.operationStack.push(operation)

    this.timers.set(operation, {
      startTime,
      startTimestamp,
      metadata,
      parentOperation,
      level,
    })

    const indent = '  '.repeat(level)
    const parentInfo = parentOperation ? ` (父操作: ${parentOperation})` : ''

    console.log(`🚀 [性能监控]${indent} ${operation} - 开始${parentInfo}`, {
      时间戳: new Date().toISOString(),
      层级: level,
      父操作: parentOperation || '无',
      元数据: metadata,
    })
  }

  /**
   * 结束计时
   */
  end(operation: string, metadata?: MetadataType): number | null {
    if (!this.isEnabled) return null

    const timerData = this.timers.get(operation)
    if (!timerData) {
      console.warn(`⚠️ [性能监控] 未找到操作 "${operation}" 的开始时间`)
      return null
    }

    const endTime = performance.now()
    const endTimestamp = Date.now()
    const duration = endTime - timerData.startTime

    const result: PerformanceResult = {
      operation,
      duration,
      startTime: timerData.startTime,
      startTimestamp: timerData.startTimestamp,
      endTime,
      endTimestamp,
      metadata: { ...timerData.metadata, ...metadata },
      parentOperation: timerData.parentOperation,
      level: timerData.level,
    }

    this.results.push(result)
    this.timers.delete(operation)

    // 从操作栈中移除
    const stackIndex = this.operationStack.lastIndexOf(operation)
    if (stackIndex !== -1) {
      this.operationStack.splice(stackIndex, 1)
    }

    // 格式化输出
    const durationText =
      duration < 1000 ? `${duration.toFixed(2)}ms` : `${(duration / 1000).toFixed(2)}s`
    const indent = '  '.repeat(timerData.level || 0)
    const parentInfo = timerData.parentOperation ? ` (父操作: ${timerData.parentOperation})` : ''

    console.log(`✅ [性能监控]${indent} ${operation} - 完成${parentInfo}`, {
      耗时: durationText,
      开始时间: new Date(timerData.startTimestamp).toISOString(),
      结束时间: new Date(endTimestamp).toISOString(),
      层级: timerData.level || 0,
      父操作: timerData.parentOperation || '无',
      元数据: result.metadata,
    })

    // 如果耗时超过阈值，给出警告
    if (duration > 2000) {
      console.warn(`⚠️ [性能警告]${indent} ${operation} 耗时过长: ${durationText}`)
    }

    return duration
  }

  /**
   * 记录即时操作（无需计时的操作）
   */
  record(operation: string, metadata?: MetadataType): void {
    if (!this.isEnabled) return

    const parentOperation = this.operationStack[this.operationStack.length - 1]
    const level = this.operationStack.length
    const indent = '  '.repeat(level)
    const parentInfo = parentOperation ? ` (父操作: ${parentOperation})` : ''

    console.log(`📝 [性能监控]${indent} ${operation}${parentInfo}`, {
      时间戳: new Date().toISOString(),
      层级: level,
      父操作: parentOperation || '无',
      元数据: metadata,
    })
  }

  /**
   * 获取所有性能结果
   */
  getResults(): PerformanceResult[] {
    return [...this.results]
  }

  /**
   * 获取指定操作的结果
   */
  getResult(operation: string): PerformanceResult | undefined {
    return this.results.find((result) => result.operation === operation)
  }

  /**
   * 清空所有监控数据
   */
  clear(): void {
    this.timers.clear()
    this.results.length = 0
    this.operationStack.length = 0
    if (this.isEnabled) {
      console.log('🧹 [性能监控] 已清空所有监控数据')
    }
  }

  /**
   * 生成性能报告
   */
  generateReport(): void {
    if (!this.isEnabled || this.results.length === 0) {
      return
    }

    console.group('📊 [性能报告] 门店选择器性能分析')

    // 按耗时排序
    const sortedResults = [...this.results].sort((a, b) => b.duration - a.duration)

    // 按层级和时间顺序显示操作树
    console.log('🌳 [操作树] 按执行顺序和层级关系:')
    const timeOrderedResults = [...this.results].sort((a, b) => a.startTime - b.startTime)
    timeOrderedResults.forEach((result) => {
      const indent = '  '.repeat(result.level || 0)
      const durationText =
        result.duration < 1000
          ? `${result.duration.toFixed(2)}ms`
          : `${(result.duration / 1000).toFixed(2)}s`
      const parentInfo = result.parentOperation ? ` ← ${result.parentOperation}` : ''
      console.log(`${indent}├─ ${result.operation} (${durationText})${parentInfo}`)
    })

    console.log('\n📋 [详细数据表]:')
    console.table(
      sortedResults.map((result) => ({
        操作: result.operation,
        耗时:
          result.duration < 1000
            ? `${result.duration.toFixed(2)}ms`
            : `${(result.duration / 1000).toFixed(2)}s`,
        层级: result.level || 0,
        父操作: result.parentOperation || '无',
        开始时间: new Date(result.startTimestamp).toLocaleTimeString(),
        结束时间: new Date(result.endTimestamp).toLocaleTimeString(),
      })),
    )

    // 计算总耗时
    const totalDuration = this.results.reduce((sum, result) => sum + result.duration, 0)
    const totalText =
      totalDuration < 1000
        ? `${totalDuration.toFixed(2)}ms`
        : `${(totalDuration / 1000).toFixed(2)}s`

    console.log(`⏱️ 总耗时: ${totalText}`)

    // 找出最耗时的操作
    if (sortedResults.length > 0) {
      const slowest = sortedResults[0]
      const slowestText =
        slowest.duration < 1000
          ? `${slowest.duration.toFixed(2)}ms`
          : `${(slowest.duration / 1000).toFixed(2)}s`
      console.log(`🐌 最耗时操作: ${slowest.operation} (${slowestText})`)
    }

    console.groupEnd()
  }
}

// ==================== 导出 ====================

/**
 * 性能监控实例
 */
export const performanceMonitor = PerformanceMonitor.getInstance()

/**
 * 将性能监控实例暴露到全局，方便在控制台中使用
 */
if (typeof window !== 'undefined') {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ;(window as any).performanceMonitor = performanceMonitor
}

/**
 * 性能监控装饰器
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function withPerformanceMonitoring<T extends (...args: any[]) => any>(
  operation: string,
  fn: T,
  getMetadata?: (...args: Parameters<T>) => MetadataType,
): T {
  return ((...args: Parameters<T>) => {
    const metadata = getMetadata?.(...args)
    performanceMonitor.start(operation, metadata)

    const result = fn(...args)

    // 如果是Promise，等待完成后结束计时
    if (result instanceof Promise) {
      return result.finally(() => {
        performanceMonitor.end(operation)
      })
    } else {
      performanceMonitor.end(operation)
      return result
    }
  }) as T
}

/**
 * 异步函数性能监控装饰器
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function withAsyncPerformanceMonitoring<T extends (...args: any[]) => Promise<any>>(
  operation: string,
  fn: T,
  getMetadata?: (...args: Parameters<T>) => MetadataType,
): T {
  return (async (...args: Parameters<T>) => {
    const metadata = getMetadata?.(...args)
    performanceMonitor.start(operation, metadata)

    try {
      const result = await fn(...args)
      performanceMonitor.end(operation, { 成功: true })
      return result
    } catch (error) {
      performanceMonitor.end(operation, { 成功: false, 错误: String(error) })
      throw error
    }
  }) as T
}

export default PerformanceMonitor
