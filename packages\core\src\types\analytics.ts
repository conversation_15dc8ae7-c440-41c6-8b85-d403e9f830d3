/**
 * 埋点相关类型定义
 */

// 埋点配置接口
export interface AnalyticsConfig {
  user_unique_id?: string | null
  app_id?: number
  channel_domain?: string
  log?: boolean
  autotrack?: boolean
  enable_debug?: boolean
  [key: string]: unknown
}

// 埋点事件属性类型
export type AnalyticsProperties = Record<string, string | number | boolean | undefined | null>

// 埋点函数类型定义
export type CollectEventFunction = {
  // 基础配置调用：collectEvent('config', config)
  (eventType: 'config', config?: AnalyticsConfig): void
  // 初始化调用：collectEvent('init', config)
  (eventType: 'init', config?: AnalyticsConfig): void
  // 开始收集：collectEvent('start')
  (eventType: 'start'): void
  // 自定义事件：collectEvent(eventName, properties)
  (eventName: string, properties?: AnalyticsProperties): void
  // 高级事件：collectEvent('beconEvent', eventName, properties)
  (eventType: 'beconEvent', eventName: string, properties?: AnalyticsProperties): void
  // 队列属性（用于SDK初始化前的调用缓存）
  q?: unknown[]
  l?: number
}

// 扩展Window接口
declare global {
  interface Window {
    collectEvent?: CollectEventFunction
    LogAnalyticsObject?: string
  }
}
